# cleanup rules for node modules, .gitignore style

# native node modules

nan/**
*/node_modules/nan/**

fsevents/binding.gyp
fsevents/fsevents.cc
fsevents/build/**
fsevents/src/**
fsevents/test/**
!fsevents/**/*.node

@vscode/spdlog/binding.gyp
@vscode/spdlog/build/**
@vscode/spdlog/deps/**
@vscode/spdlog/src/**
@vscode/spdlog/test/**
@vscode/spdlog/*.yml
!@vscode/spdlog/build/Release/*.node

@vscode/deviceid/binding.gyp
@vscode/deviceid/build/**
@vscode/deviceid/deps/**
@vscode/deviceid/src/**
@vscode/deviceid/test/**
@vscode/deviceid/*.yml
!@vscode/deviceid/build/Release/*.node


@vscode/sqlite3/binding.gyp
@vscode/sqlite3/benchmark/**
@vscode/sqlite3/cloudformation/**
@vscode/sqlite3/deps/**
@vscode/sqlite3/test/**
@vscode/sqlite3/build/**
@vscode/sqlite3/src/**
!@vscode/sqlite3/build/Release/*.node

@vscode/windows-mutex/binding.gyp
@vscode/windows-mutex/build/**
@vscode/windows-mutex/src/**
!@vscode/windows-mutex/**/*.node

@vscode/windows-process-tree/binding.gyp
@vscode/windows-process-tree/build/**
@vscode/windows-process-tree/src/**
@vscode/windows-process-tree/tsconfig.json
@vscode/windows-process-tree/tslint.json
!@vscode/windows-process-tree/**/*.node

@vscode/windows-registry/binding.gyp
@vscode/windows-registry/src/**
@vscode/windows-registry/build/**
!@vscode/windows-registry/build/Release/*.node

@vscode/tree-sitter-wasm/wasm/tree-sitter-*.wasm
!@vscode/tree-sitter-wasm/wasm/tree-sitter-typescript.wasm
!@vscode/tree-sitter-wasm/wasm/tree-sitter-regex.wasm
!@vscode/tree-sitter-wasm/wasm/tree-sitter-ini.wasm
!@vscode/tree-sitter-wasm/wasm/tree-sitter-css.wasm

native-keymap/binding.gyp
native-keymap/build/**
native-keymap/src/**
native-keymap/deps/**
!native-keymap/build/Release/*.node

native-is-elevated/binding.gyp
native-is-elevated/build/**
native-is-elevated/src/**
native-is-elevated/deps/**
!native-is-elevated/build/Release/*.node

native-watchdog/binding.gyp
native-watchdog/build/**
native-watchdog/src/**
!native-watchdog/build/Release/*.node

@vscode/vsce-sign/**
!@vscode/vsce-sign/src/main.d.ts
!@vscode/vsce-sign/src/main.js
!@vscode/vsce-sign/package.json
!@vscode/vsce-sign/bin/**

windows-foreground-love/binding.gyp
windows-foreground-love/build/**
windows-foreground-love/src/**
!windows-foreground-love/**/*.node

kerberos/binding.gyp
kerberos/build/**
kerberos/src/**
kerberos/node_modules/**
!kerberos/**/*.node

node-pty/binding.gyp
node-pty/build/**
node-pty/src/**
node-pty/lib/*.test.js
node-pty/tools/**
node-pty/deps/**
node-pty/scripts/**
node-pty/third_party/**
!node-pty/build/Release/spawn-helper
!node-pty/build/Release/*.exe
!node-pty/build/Release/*.dll
!node-pty/build/Release/*.node
!node-pty/build/Release/conpty/conpty.dll
!node-pty/build/Release/conpty/OpenConsole.exe

@parcel/watcher/binding.gyp
@parcel/watcher/build/**
@parcel/watcher/prebuilds/**
@parcel/watcher/src/**
!@parcel/watcher/build/Release/*.node

vsda/**
!vsda/index.js
!vsda/index.d.ts
!vsda/package.json
!vsda/build/Release/vsda.node
!vsda/rust/web/**
!vsda/rust/bundler/**

@vscode/policy-watcher/build/**
@vscode/policy-watcher/.husky/**
@vscode/policy-watcher/src/**
@vscode/policy-watcher/binding.gyp
@vscode/policy-watcher/README.md
@vscode/policy-watcher/index.d.ts
!@vscode/policy-watcher/build/Release/vscode-policy-watcher.node

@vscode/windows-ca-certs/**/*
!@vscode/windows-ca-certs/package.json
!@vscode/windows-ca-certs/**/*.node

@vscode/node-addon-api/**/*
node-addon-api/**/*
prebuild-install/**/*

# other node modules

**/docs/**
**/example/**
**/examples/**
**/test/**
**/tests/**

**/History.md
**/CHANGELOG.md
**/README.md
**/readme.md
**/readme.markdown
**/CODE_OF_CONDUCT.md
**/SUPPORT.md
**/CONTRIBUTING.md

**/*.ts

# Exclude TS files that aren't needed by TS extension
typescript/lib/tsc.js
typescript/lib/typescriptServices.js
typescript/lib/tsserverlibrary.js
# We still need to include stdlib d.ts
!typescript/lib/lib*.d.ts

jschardet/index.js
jschardet/src/**
jschardet/dist/jschardet.js

es6-promise/lib/**

vscode-textmate/webpack.config.js

zone.js/dist/**
!zone.js/dist/zone-node.js

# https://github.com/xtermjs/xterm.js/issues/3137
@xterm/xterm/src/**
@xterm/xterm/tsconfig.all.json

# https://github.com/xtermjs/xterm.js/issues/3138
@xterm/xterm-addon-*/src/**
@xterm/xterm-addon-*/fixtures/**
@xterm/xterm-addon-*/out/**
@xterm/xterm-addon-*/out-test/**
