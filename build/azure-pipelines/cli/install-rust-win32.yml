parameters:
  - name: channel
    type: string
    default: 1.85
  - name: targets
    default: []
    type: object

# Todo: use 1ES pipeline once extension is installed in ADO

steps:
  - task: RustInstaller@1
    inputs:
      rustVersion: ms-${{ parameters.channel }}
      cratesIoFeedOverride: $(CARGO_REGISTRY)
      additionalTargets: ${{ join(' ', parameters.targets) }}
      toolchainFeed: https://pkgs.dev.azure.com/monacotools/Monaco/_packaging/vscode/nuget/v3/index.json
      default: true
      addToPath: true
    displayName: Install MSFT Rust
    condition: and(succeeded(), ne(variables['CARGO_REGISTRY'], 'none'))

  - powershell: |
      . build/azure-pipelines/win32/exec.ps1
      Invoke-WebRequest -Uri "https://win.rustup.rs" -Outfile $(Build.ArtifactStagingDirectory)/rustup-init.exe
      exec { $(Build.ArtifactStagingDirectory)/rustup-init.exe -y --profile minimal --default-toolchain $env:RUSTUP_TOOLCHAIN --default-host x86_64-pc-windows-msvc }
      echo "##vso[task.prependpath]$env:USERPROFILE\.cargo\bin"
    env:
      RUSTUP_TOOLCHAIN: ${{ parameters.channel }}
    displayName: Install OSS Rust
    condition: and(succeeded(), eq(variables['CARGO_REGISTRY'], 'none'))

  - powershell: |
      . build/azure-pipelines/win32/exec.ps1
      exec { rustup default $RUSTUP_TOOLCHAIN }
      exec { rustup update $RUSTUP_TOOLCHAIN }
    env:
      RUSTUP_TOOLCHAIN: ${{ parameters.channel }}
    displayName: "Set Rust version"
    condition: and(succeeded(), eq(variables['CARGO_REGISTRY'], 'none'))

  - ${{ each target in parameters.targets }}:
    - script: rustup target add ${{ target }}
      displayName: "Adding Rust target '${{ target }}'"
      condition: and(succeeded(), eq(variables['CARGO_REGISTRY'], 'none'))

  - powershell: |
      . build/azure-pipelines/win32/exec.ps1
      exec { rustc --version }
      exec { cargo --version }
    displayName: "Check Rust versions"
