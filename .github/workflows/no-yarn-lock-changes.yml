name: Prevent yarn.lock changes in PRs

on: pull_request
permissions: {}

jobs:
  main:
    name: Prevent yarn.lock changes in PRs
    runs-on: ubuntu-latest
    steps:
      - name: Get file changes
        uses: trilom/file-changes-action@a6ca26c14274c33b15e6499323aac178af06ad4b # v1.2.4
        id: file_changes
      - name: Check if lockfiles were modified
        id: lockfile_check
        run: |
          if cat $HOME/files.json | jq -e 'any(test("yarn\\.lock$|Cargo\\.lock$"))' > /dev/null; then
            echo "lockfiles_modified=true" >> $GITHUB_OUTPUT
            echo "Lockfiles were modified in this PR"
          else
            echo "lockfiles_modified=false" >> $GITHUB_OUTPUT
            echo "No lockfiles were modified in this PR"
          fi
      - name: Prevent Copilot from modifying lockfiles
        if: ${{ steps.lockfile_check.outputs.lockfiles_modified == 'true' && github.event.pull_request.user.login == 'Copilot' }}
        run: |
          echo "Copilot is not allowed to modify yarn.lock or Cargo.lock files."
          echo "If you need to update dependencies, please do so manually or through authorized means."
          exit 1
      - uses: octokit/request-action@dad4362715b7fb2ddedf9772c8670824af564f0d # v2.4.0
        id: get_permissions
        if: ${{ steps.lockfile_check.outputs.lockfiles_modified == 'true' && github.event.pull_request.user.login != 'Copilot' }}
        with:
          route: GET /repos/microsoft/vscode/collaborators/{username}/permission
          username: ${{ github.event.pull_request.user.login }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Set control output variable
        id: control
        if: ${{ steps.lockfile_check.outputs.lockfiles_modified == 'true' && github.event.pull_request.user.login != 'Copilot' }}
        run: |
          echo "user: ${{ github.event.pull_request.user.login }}"
          echo "role: ${{ fromJson(steps.get_permissions.outputs.data).permission }}"
          echo "is dependabot: ${{ github.event.pull_request.user.login == 'dependabot[bot]' }}"
          echo "should_run: ${{ !contains(fromJson('["admin", "maintain", "write"]'), fromJson(steps.get_permissions.outputs.data).permission) }}"
          echo "should_run=${{ !contains(fromJson('["admin", "maintain", "write"]'), fromJson(steps.get_permissions.outputs.data).permission) && github.event.pull_request.user.login != 'dependabot[bot]' }}" >> $GITHUB_OUTPUT
      - name: Check for lockfile changes
        if: ${{ steps.lockfile_check.outputs.lockfiles_modified == 'true' && steps.control.outputs.should_run == 'true' }}
        run: |
          echo "Changes to yarn.lock/Cargo.lock files aren't allowed in PRs."
          exit 1
