[{"type": "comment", "name": "question", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "updateLabels", "addLabel": "*question"}, {"type": "comment", "name": "dev-question", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "updateLabels", "addLabel": "*dev-question"}, {"type": "label", "name": "*question", "action": "close", "reason": "not_planned", "comment": "We closed this issue because it is a question about using VS Code rather than an issue or feature request. Please search for help on [StackOverflow](https://aka.ms/vscodestackoverflow), where the community has already answered thousands of similar questions. You may find their [guide on asking a new question](https://aka.ms/vscodestackoverflowquestion) helpful if your question has not already been asked. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting).\n\nHappy Coding!"}, {"type": "label", "name": "*dev-question", "action": "close", "reason": "not_planned", "comment": "We have a great extension developer community over on [GitHub discussions](https://github.com/microsoft/vscode-discussions/discussions) and [Slack](https://vscode-dev-community.slack.com/) where extension authors help each other. This is a great place for you to ask questions and find support.\n\nHappy Coding!"}, {"type": "label", "name": "*extension-candidate", "action": "close", "reason": "not_planned", "comment": "We try to keep VS Code lean and we think the functionality you're asking for is great for a VS Code extension. Maybe you can already find one that suits you in the [VS Code Marketplace](https://aka.ms/vscodemarketplace). Just in case, in a few simple steps you can get started [writing your own extension](https://aka.ms/vscodewritingextensions). See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting).\n\nHappy Coding!"}, {"type": "label", "name": "*not-reproducible", "action": "close", "reason": "not_planned", "comment": "We closed this issue because we are unable to reproduce the problem with the steps you describe. Chances are we've already fixed your problem in a recent version of VS Code. If not, please ask us to reopen the issue and provide us with more detail. Our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) might help you with that.\n\nHappy Coding!"}, {"type": "label", "name": "*out-of-scope", "action": "close", "reason": "not_planned", "comment": "We closed this issue because we [don't plan to address it](https://aka.ms/vscode-out-of-scope) in the foreseeable future. If you disagree and feel that this issue is crucial: we are happy to listen and to reconsider.\n\nIf you wonder what we are up to, please see our [roadmap](https://aka.ms/vscoderoadmap) and [issue reporting guidelines](https://aka.ms/vscodeissuereporting).\n\nThanks for your understanding, and happy coding!"}, {"type": "label", "name": "wont-fix", "action": "close", "reason": "not_planned", "comment": "We closed this issue because we [don't plan to address it](https://github.com/microsoft/vscode/wiki/Issue-Grooming#wont-fix-bugs).\n\nThanks for your understanding, and happy coding!"}, {"type": "comment", "name": "causedByExtension", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "updateLabels", "addLabel": "*caused-by-extension"}, {"type": "label", "name": "*caused-by-extension", "action": "close", "reason": "not_planned", "comment": "This issue is caused by an extension, please file it with the repository (or contact) the extension has linked in its overview in VS Code or the [marketplace](https://aka.ms/vscodemarketplace) for VS Code. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting). If you don't know which extension is causing the problem, you can run `Help: Start extension bisect` from the command palette (F1) to help identify the problem extension.\n\nHappy Coding!"}, {"type": "label", "name": "*as-designed", "action": "close", "reason": "not_planned", "comment": "The described behavior is how it is expected to work. If you disagree, please explain what is expected and what is not in more detail. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting).\n\nHappy Coding!"}, {"type": "label", "name": "L10N", "assign": ["csigs", "<PERSON><PERSON><PERSON><PERSON>"]}, {"type": "comment", "name": "duplicate", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "updateLabels", "addLabel": "*duplicate"}, {"type": "label", "name": "*duplicate", "action": "close", "reason": "not_planned", "comment": "Thanks for creating this issue! We figured it's covering the same as another one we already have. Thus, we closed this one as a duplicate. You can search for [similar existing issues](${duplicateQuery}). See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting).\n\nHappy Coding!"}, {"type": "comment", "name": "verified", "allowUsers": ["@author"], "action": "updateLabels", "addLabel": "verified", "removeLabel": "author-verification-requested", "requireLabel": "author-verification-requested", "disallowLabel": "unreleased"}, {"type": "comment", "name": "confirm", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "updateLabels", "addLabel": "confirmed", "removeLabel": "confirmation-pending"}, {"type": "comment", "name": "confirmationPending", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "updateLabels", "addLabel": "confirmation-pending", "removeLabel": "confirmed"}, {"type": "comment", "name": "needsMoreInfo", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "updateLabels", "addLabel": "~info-needed"}, {"type": "comment", "name": "needsPerfInfo", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "addLabel": "info-needed", "comment": "Thanks for creating this issue regarding performance! Please follow this guide to help us diagnose performance issues: https://github.com/microsoft/vscode/wiki/Performance-Issues \n\nHappy Coding!"}, {"type": "comment", "name": "jsDebugLogs", "action": "updateLabels", "addLabel": "info-needed", "comment": "Please collect trace logs using the following instructions:\n\n> If you're able to, add `\"trace\": true` to your `launch.json` and reproduce the issue. The location of the log file on your disk will be written to the Debug Console. Share that with us.\n>\n> ⚠️ This log file will not contain source code, but will contain file paths. You can drop it into https://microsoft.github.io/vscode-pwa-analyzer/index.html to see what it contains. If you'd rather not share the log publicly, you can email <NAME_EMAIL>"}, {"type": "comment", "name": "closedWith", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "completed", "addLabel": "unreleased"}, {"type": "comment", "name": "spam", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "invalid"}, {"type": "comment", "name": "a11ymas", "allowUsers": ["AccessibilityTestingTeam-TCS", "dixitsonali95", "Mohini78", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mspatil110", "umasarath52", "v-umnaik"], "action": "updateLabels", "addLabel": "a11ymas"}, {"type": "label", "name": "*off-topic", "action": "close", "reason": "not_planned", "comment": "Thanks for creating this issue. We think this issue is unactionable or unrelated to the goals of this project. Please follow our [issue reporting guidelines](https://aka.ms/vscodeissuereporting).\n\nHappy Coding!"}, {"type": "comment", "name": "extPython", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the Python extension. Please file the issue to the [Python extension repository](https://github.com/microsoft/vscode-python). Make sure to check their issue reporting template and provide them relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extJupyter", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the <PERSON>py<PERSON> extension. Please file the issue to the [Jupyter extension repository](https://github.com/microsoft/vscode-jupyter). Make sure to check their issue reporting template and provide them relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extC", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the C extension. Please file the issue to the [C extension repository](https://github.com/microsoft/vscode-cpptools). Make sure to check their issue reporting template and provide them relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extC++", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the C++ extension. Please file the issue to the [C++ extension repository](https://github.com/microsoft/vscode-cpptools). Make sure to check their issue reporting template and provide them relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extCpp", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the C++ extension. Please file the issue to the [C++ extension repository](https://github.com/microsoft/vscode-cpptools). Make sure to check their issue reporting template and provide them relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extTS", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the TypeScript language service. Please file the issue to the [TypeScript repository](https://github.com/microsoft/TypeScript/). Make sure to check their [contributing guidelines](https://github.com/microsoft/TypeScript/blob/master/CONTRIBUTING.md) and provide relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extJS", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the TypeScript/JavaScript language service. Please file the issue to the [TypeScript repository](https://github.com/microsoft/TypeScript/). Make sure to check their [contributing guidelines](https://github.com/microsoft/TypeScript/blob/master/CONTRIBUTING.md) and provide relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extC#", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the C# extension. Please file the issue to the [C# extension repository](https://github.com/OmniSharp/omnisharp-vscode.git). Make sure to check their issue reporting template and provide them relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extGo", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the Go extension. Please file the issue to the [Go extension repository](https://github.com/golang/vscode-go). Make sure to check their issue reporting template and provide them relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extPowershell", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the PowerShell extension. Please file the issue to the [PowerShell extension repository](https://github.com/PowerShell/vscode-powershell). Make sure to check their issue reporting template and provide them relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extLiveShare", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the LiveShare extension. Please file the issue to the [LiveShare repository](https://github.com/MicrosoftDocs/live-share). Make sure to check their [contributing guidelines](https://github.com/MicrosoftDocs/live-share/blob/master/CONTRIBUTING.md) and provide relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extDocker", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the Docker extension. Please file the issue to the [Docker extension repository](https://github.com/microsoft/vscode-docker). Make sure to check their issue reporting template and provide them relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extJava", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the Java extension. Please file the issue to the [Java extension repository](https://github.com/redhat-developer/vscode-java). Make sure to check their [troubleshooting instructions](https://github.com/redhat-developer/vscode-java/wiki/Troubleshooting) and provide relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extJavaDebug", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the Java Debugger extension. Please file the issue to the [Java Debugger repository](https://github.com/microsoft/vscode-java-debug). Make sure to check their issue reporting template and provide them relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extCodespaces", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the Codespaces extension. Please file the issue in the [Codespaces Discussion Forum](http://aka.ms/ghcs-feedback). Make sure to check their issue reporting template and provide them relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "extCopilot", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "close", "reason": "not_planned", "addLabel": "*caused-by-extension", "comment": "It looks like this is caused by the Copilot extension. Please file the issue in the [Copilot Discussion Forum](https://github.com/community/community/discussions/categories/copilot). Make sure to check their issue reporting template and provide them relevant information such as the extension version you're using. See also our [issue reporting guidelines](https://aka.ms/vscodeissuereporting) for more information.\n\nHappy Coding!"}, {"type": "comment", "name": "gifPlease", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "comment", "addLabel": "info-needed", "comment": "Thanks for reporting this issue! Unfortunately, it's hard for us to understand what issue you're seeing. Please help us out by providing a screen recording showing exactly what isn't working as expected. While we can work with most standard formats, `.gif` files are preferred as they are displayed inline on GitHub. You may find https://gifcap.dev helpful as a browser-based gif recording tool.\n\nIf the issue depends on keyboard input, you can help us by enabling screencast mode for the recording (`Developer: Toggle Screencast Mode` in the command palette). Lastly, please attach this file via the GitHub web interface as emailed responses will strip files out from the issue.\n\nHappy coding!"}, {"type": "comment", "name": "confirmPlease", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"], "action": "comment", "addLabel": "info-needed", "comment": "Please diagnose the root cause of the issue by running the command `F1 > Help: Troubleshoot Issue` and following the instructions. Once you have done that, please update the issue with the results.\n\nHappy Coding!"}, {"__comment__": "Allows folks on the team to label issues by commenting: `\\label My-Label` ", "type": "comment", "name": "label", "allowUsers": []}, {"type": "comment", "name": "assign", "allowUsers": ["cle<PERSON><PERSON>", "usernamehw", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IllusionMH"]}, {"type": "label", "name": "*workspace-trust-docs", "action": "close", "reason": "not_planned", "comment": "This issue appears to be the result of the new workspace trust feature shipped in June 2021. This security-focused feature has major impact on the functionality of VS Code. Due to the volume of issues, we ask that you take some time to review our [comprehensive documentation](https://aka.ms/vscode-workspace-trust) on the feature. If your issue is still not resolved, please let us know."}, {"type": "label", "name": "~verification-steps-needed", "action": "updateLabels", "addLabel": "verification-steps-needed", "removeLabel": "~verification-steps-needed", "comment": "Friendly ping! Looks like this issue requires some further steps to be verified. Please provide us with the steps necessary to verify this issue."}, {"type": "label", "name": "~info-needed", "action": "updateLabels", "addLabel": "info-needed", "removeLabel": "~info-needed", "comment": "Thanks for creating this issue! We figured it's missing some basic information or in some other way doesn't follow our [issue reporting guidelines](https://aka.ms/vscodeissuereporting). Please take the time to review these and update the issue.\n\nFor Copilot Issues, be sure to visit our [Copilot-specific guidelines](https://github.com/microsoft/vscode/wiki/Copilot-Issues) page for details on the necessary information.\n\nHappy Coding!"}, {"type": "label", "name": "~version-info-needed", "action": "updateLabels", "addLabel": "info-needed", "removeLabel": "~version-info-needed", "comment": "Thanks for creating this issue! We figured it's missing some basic information, such as a version number, or in some other way doesn't follow our [issue reporting guidelines](https://aka.ms/vscodeissuereporting). Please take the time to review these and update the issue.\n\nHappy Coding!"}, {"type": "label", "name": "~confirmation-needed", "action": "updateLabels", "addLabel": "info-needed", "removeLabel": "~confirmation-needed", "comment": "Please diagnose the root cause of the issue by running the command `F1 > Help: Troubleshoot Issue` and following the instructions. Once you have done that, please update the issue with the results.\n\nHappy Coding!"}, {"type": "label", "name": "~chat-rate-limiting", "removeLabel": "~chat-rate-limiting", "action": "close", "reason": "not_planned", "comment": "This issue is a duplicate of https://github.com/microsoft/vscode-copilot-release/issues/2627. Please refer to that issue for updates and discussions. Feel free to open a new issue if you think this is a different problem."}, {"type": "label", "name": "~chat-request-failed", "removeLabel": "~chat-request-failed", "action": "close", "reason": "not_planned", "comment": "This issue is a duplicate of https://github.com/microsoft/vscode-copilot-release/issues/4332. Please refer to that issue for updates and discussions. Feel free to open a new issue if you think this is a different problem."}, {"type": "label", "name": "~chat-rai-content-filters", "removeLabel": "~chat-rai-content-filters", "action": "close", "reason": "not_planned", "comment": "This issue is a duplicate of https://github.com/microsoft/vscode-copilot-release/issues/2625. Please refer to that issue for updates and discussions. Feel free to open a new issue if you think this is a different problem."}, {"type": "label", "name": "~chat-public-code-blocking", "removeLabel": "~chat-public-code-blocking", "action": "close", "reason": "not_planned", "comment": "This issue is a duplicate of https://github.com/microsoft/vscode-copilot-release/issues/2626. Please refer to that issue for updates and discussions. Feel free to open a new issue if you think this is a different problem."}, {"type": "label", "name": "~chat-lm-unavailable", "removeLabel": "~chat-lm-unavailable", "action": "close", "reason": "not_planned", "comment": "This issue is a duplicate of https://github.com/microsoft/vscode-copilot-release/issues/2116. Please refer to that issue for updates and discussions. Feel free to open a new issue if you think this is a different problem."}, {"type": "label", "name": "~chat-authentication", "removeLabel": "~chat-authentication", "action": "close", "reason": "not_planned", "comment": "Please look at the following meta issue: https://github.com/microsoft/vscode-copilot-release/issues/11078, if the bug you are experiencing is not there, please comment on this closed issue thread so we can re-open it.", "assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"type": "label", "name": "~chat-no-response-returned", "removeLabel": "~chat-no-response-returned", "action": "close", "reason": "not_planned", "comment": "Please look at the following meta issue: https://github.com/microsoft/vscode-copilot-release/issues/7034. Please refer to that issue for updates and discussions. Feel free to open a new issue if you think this is a different problem."}, {"type": "label", "name": "~chat-billing", "removeLabel": "~chat-billing", "addLabel": "chat-billing", "action": "close", "reason": "not_planned", "comment": "Please look at the following meta issue: https://github.com/microsoft/vscode/issues/252230. Please refer to that issue for updates and discussions. Feel free to open a new issue if you think this is a different problem."}]