---
description: 'Save learnings from conversation'
tools: ['codebase', 'editFiles', 'githubRepo', 'runCommands', 'search', 'searchResults', 'usages']
---
Please take a moment and deeply reflect on all the steps you took and think if there would have been a piece of information which would have allowed you to work faster (take less steps).

The file .vscode/project.instructions.md has been already provided to you. Edit the file such that it would contain information which would have made you work faster. Please don't make this too specific to this task, but rather something that is generic but useful enought to ensure speed-ups in the future for other tasks.
