{"name": "extension-editing", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "^1.4.0"}, "icon": "images/icon.png", "activationEvents": ["onLanguage:json", "onLanguage:markdown"], "main": "./out/extensionEditingMain", "browser": "./dist/browser/extensionEditingBrowserMain", "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "scripts": {"compile": "gulp compile-extension:extension-editing", "watch": "gulp watch-extension:extension-editing"}, "dependencies": {"jsonc-parser": "^3.2.0", "markdown-it": "^12.3.2", "parse5": "^3.0.2"}, "contributes": {"jsonValidation": [{"fileMatch": "package.json", "url": "vscode://schemas/vscode-extensions"}, {"fileMatch": "*language-configuration.json", "url": "vscode://schemas/language-configuration"}, {"fileMatch": ["*icon-theme.json", "!*product-icon-theme.json"], "url": "vscode://schemas/icon-theme"}, {"fileMatch": "*product-icon-theme.json", "url": "vscode://schemas/product-icon-theme"}, {"fileMatch": "*color-theme.json", "url": "vscode://schemas/color-theme"}], "languages": [{"id": "ignore", "filenames": [".vscodeign<PERSON>"]}]}, "devDependencies": {"@types/markdown-it": "0.0.2", "@types/node": "22.x"}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}